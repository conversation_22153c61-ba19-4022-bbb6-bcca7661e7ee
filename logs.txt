[  +52 ms] ══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════
           The following assertion was thrown while finalizing the widget tree:
           _HomeScreenState#84507(ticker active) was disposed with an active Ticker.
           _HomeScreenState created a Ticker via its SingleTickerProviderStateMixin, but at the time dispose()
           was called on the mixin, that Ticker was still active. The Ticker must be disposed before calling
           super.dispose().
           Tickers used by AnimationControllers should be disposed by calling dispose() on the
           AnimationController itself. Otherwise, the ticker will leak.
           The offending ticker was:
             Ticker(created by _HomeScreenState#84507)
             The stack trace when the Ticker was actually created was:
             #0      new Ticker.<anonymous closure> (package:flutter/src/scheduler/ticker.dart:86:40)
             #1      new Ticker (package:flutter/src/scheduler/ticker.dart:88:6)
             #2      SingleTickerProviderStateMixin.createTicker
             (package:flutter/src/widgets/ticker_provider.dart:215:15)
             #3      new AnimationController (package:flutter/src/animation/animation_controller.dart:257:21)
             #4      RideProvider.setLoadingController (package:sepesha_app/provider/ride_provider.dart:164:26)
             #5      _HomeScreenState.initState.<anonymous closure>
             (package:sepesha_app/screens/dashboard/home_screen.dart:54:17)
             #6      SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)
             #7      SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1365:11)
             #8      SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1204:5)
             #9      _invoke (dart:ui/hooks.dart:331:13)
             #10     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
             #11     _drawFrame (dart:ui/hooks.dart:303:31)

           When the exception was thrown, this was the stack:
           #0      SingleTickerProviderStateMixin.dispose.<anonymous closure> (package:flutter/src/widgets/ticker_provider.dart:230:7)
           #1      SingleTickerProviderStateMixin.dispose (package:flutter/src/widgets/ticker_provider.dart:244:6)
           #2      _HomeScreenState.dispose (package:sepesha_app/screens/dashboard/home_screen.dart:83:11)
           #3      StatefulElement.unmount (package:flutter/src/widgets/framework.dart:5922:11)
           #4      _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2075:13)
           #5      _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #6      SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #7      _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #8      _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #9      SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #10     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #11     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #12     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #13     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #14     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #15     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #16     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #17     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #18     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #19     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #20     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #21     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #22     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #23     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #24     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #25     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #26     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #27     List.forEach (dart:core-patch/growable_array.dart:425:8)
           #28     SliverMultiBoxAdaptorElement.visitChildren (package:flutter/src/widgets/sliver.dart:1197:52)
           #29     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #30     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #31     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #32     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #33     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #34     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #35     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #36     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #37     MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7118:16)
           #38     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #39     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #40     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #41     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #42     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #43     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #44     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #45     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #46     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #47     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #48     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #49     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #50     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #51     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #52     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #53     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #54     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #55     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #56     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #57     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #58     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #59     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #60     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #61     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #62     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #63     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #64     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #65     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #66     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #67     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #68     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #69     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #70     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #71     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #72     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #73     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #74     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #75     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #76     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #77     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #78     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #79     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #80     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #81     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #82     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #83     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #84     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #85     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #86     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #87     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #88     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #89     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #90     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #91     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #92     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #93     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #94     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #95     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #96     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #97     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #98     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #99     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #100    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #101    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #102    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #103    MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7118:16)
           #104    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #105    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #106    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #107    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #108    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #109    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #110    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #111    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #112    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #113    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #114    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #115    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #116    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #117    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #118    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #119    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #120    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #121    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #122    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #123    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #124    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #125    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #126    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #127    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #128    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #129    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #130    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #131    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #132    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #133    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #134    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #135    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #136    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #137    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #138    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #139    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #140    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #141    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #142    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #143    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #144    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #145    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #146    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #147    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #148    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #149    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #150    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #151    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #152    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #153    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #154    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #155    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #156    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #157    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #158    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #159    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #160    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #161    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #162    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #163    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #164    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #165    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #166    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #167    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #168    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #169    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #170    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #171    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #172    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #173    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #174    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #175    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #176    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #177    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #178    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #179    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #180    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #181    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #182    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #183    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #184    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #185    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #186    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #187    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #188    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #189    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #190    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #191    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #192    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #193    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #194    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #195    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #196    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #197    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #198    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #199    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #200    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #201    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #202    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #203    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #204    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #205    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #206    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #207    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #208    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #209    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #210    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #211    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #212    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #213    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #214    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #215    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #216    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #217    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #218    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #219    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #220    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #221    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #222    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #223    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #224    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #225    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #226    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #227    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #228    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #229    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #230    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #231    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #232    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #233    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #234    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #235    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #236    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #237    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #238    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #239    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #240    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #241    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #242    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #243    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #244    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #245    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #246    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #247    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #248    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #249    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #250    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #251    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #252    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #253    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #254    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #255    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #256    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #257    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #258    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #259    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #260    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #261    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #262    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #263    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #264    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #265    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #266    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #267    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #268    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #269    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #270    ListIterable.forEach (dart:_internal/iterable.dart:49:13)
           #271    _InactiveElements._unmountAll (package:flutter/src/widgets/framework.dart:2084:25)
           #272    BuildOwner.lockState (package:flutter/src/widgets/framework.dart:2965:15)
           #273    BuildOwner.finalizeTree (package:flutter/src/widgets/framework.dart:3288:7)
           #274    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1247:19)
           #275    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:495:5)
           #276    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)
           #277    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1351:9)
           #278    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1204:5)
           #279    _invoke (dart:ui/hooks.dart:331:13)
           #280    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
           #281    _drawFrame (dart:ui/hooks.dart:303:31)
           ════════════════════════════════════════════════════════════════════════════════════════════════════
[   +8 ms] I/Choreographer(22450): Skipped 46 frames!  The application may be doing too much work on its main thread.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +63 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +23 ms] I/OpenGLRenderer(22450): Davey! duration=867ms; Flags=0, FrameTimelineVsyncId=8448812, IntendedVsync=130723422450196, Vsync=130724189116832, InputEventId=0,
HandleInputStart=130724191615936, AnimationStart=130724191618644, PerformTraversalsStart=130724191970728, DrawStart=130724195347239, FrameDeadline=130723443116862, FrameInterval=130724191506874,   
FrameStartTime=16666666, SyncQueued=130724285332925, SyncStart=130724294806832, IssueDrawCommandsStart=130724294917457, SwapBuffers=130724295528551, FrameCompleted=130724299295009,
DequeueBufferDuration=28854, QueueBufferDuration=1546302, GpuCompleted=130724298366936, SwapBuffersCompleted=130724299295009, DisplayPresentTime=0, 
[  +23 ms] I/GoogleMapController(22450): Installing custom TextureView driven invalidator.
[  +48 ms] I/GoogleMapController(22450): Installing custom TextureView driven invalidator.
[ +214 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] D/Google Android Maps SDK(22450): For capability in capabilities, log:
[   +1 ms] D/Google Android Maps SDK(22450): "AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +22 ms] E/flutter (22450): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: Bad state: Future already completed
[        ] E/flutter (22450): #0      _AsyncCompleter.complete (dart:async/future_impl.dart:100:31)
[        ] E/flutter (22450): #1      _HomeScreenState._buildMap.<anonymous closure> (package:sepesha_app/screens/dashboard/home_screen.dart:115:32)
[        ] E/flutter (22450): #2      _GoogleMapState.onPlatformViewCreated (package:google_maps_flutter/src/google_map.dart:521:19)
[        ] E/flutter (22450): <asynchronous suspension>
[        ] E/flutter (22450): 
[  +12 ms] D/InputConnectionAdaptor(22450): The input method toggled cursor monitoring off
[  +48 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +1 ms] D/ImageReaderSurfaceProducer(22450): ImageTextureEntry can't wait on the fence on Android < 33
[  +37 ms] I/mpany.sepeshap(22450): Background young concurrent copying GC freed 7026KB AllocSpace bytes, 107(6932KB) LOS objects, 37% free, 16MB/27MB, paused 229us,253us total 109.618ms
[  +22 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +44 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +31 ms] I/mpany.sepeshap(22450): Compiler allocated 6714KB to compile void m140.ehv.W(m140.emf, m140.fej, android.content.res.Resources, m140.eka, m140.eut, boolean, m140.djk, java.util.Map,
boolean, boolean, boolean, boolean)
[  +10 ms] W/DynamiteModule(22450): Local module descriptor class for com.google.android.gms.googlecertificates not found.
[   +7 ms] I/DynamiteModule(22450): Considering local module com.google.android.gms.googlecertificates:0 and remote module com.google.android.gms.googlecertificates:7
[   +1 ms] I/DynamiteModule(22450): Selected remote version of com.google.android.gms.googlecertificates, version >= 7
[  +42 ms] W/mpany.sepeshap(22450): ClassLoaderContext classpath element checksum mismatch. expected=*********, found=**********
(DLC[];PCL[base.apk**********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]#PCL[/system/framework/com
.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.e
xtservices/javalib/android.ext.adservices.jar**********]} |
DLC[];PCL[/data/app/~~8j-tqKVgJBrEjShsQvNG2w==/com.sepeshacompany.sepeshapp-F8-bn1UJn1OKlTn4Whg79A==/base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]})
[ +100 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +42 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +105 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +14 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +22 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +22 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +21 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +8 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +18 ms] I/mpany.sepeshap(22450): Background concurrent copying GC freed 2975KB AllocSpace bytes, 124(9844KB) LOS objects, 66% free, 14MB/43MB, paused 416us,857us total 125.932ms
[   +5 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +26 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +3 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +16 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +121 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +70 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +148 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +193 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +137 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +163 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +873 ms] E/flutter (22450): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(channel-error, Unable to establish connection on channel:
"dev.flutter.pigeon.google_maps_flutter_android.MapsApi.animateCamera.0"., null, null)
[   +4 ms] E/flutter (22450): #0      MapsApi.animateCamera (package:google_maps_flutter_android/src/messages.g.dart:2278:7)
[   +3 ms] E/flutter (22450): <asynchronous suspension>
[   +1 ms] E/flutter (22450): 
[  +87 ms] E/flutter (22450): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(channel-error, Unable to establish connection on channel:
"dev.flutter.pigeon.google_maps_flutter_android.MapsApi.animateCamera.0"., null, null)
[        ] E/flutter (22450): #0      MapsApi.animateCamera (package:google_maps_flutter_android/src/messages.g.dart:2278:7)
[        ] E/flutter (22450): <asynchronous suspension>
[        ] E/flutter (22450): 
[   +2 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +117 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +3 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +118 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +7 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +1 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +107 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +118 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +117 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +122 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +153 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +76 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +108 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +74 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +38 ms] I/mpany.sepeshap(22450): Compiler allocated 4483KB to compile void m140.ecu.c(m140.djz, m140.ebm, m140.edp, m140.ekm, boolean)
[  +59 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +12 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] I/mpany.sepeshap(22450): Background concurrent copying GC freed 5389KB AllocSpace bytes, 24(10MB) LOS objects, 60% free, 31MB/79MB, paused 265us,227us total 190.734ms
[   +5 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +7 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] I/mpany.sepeshap(22450): Compiler allocated 5746KB to compile void m140.epo.o()
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] I/mpany.sepeshap(22450): Compiler allocated 4188KB to compile m140.emb m140.emf.l(m140.azx, m140.iai, m140.elt, byte[], boolean, m140.cdn, m140.dex, java.lang.Iterable)
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] I/mpany.sepeshap(22450): Compiler allocated 4938KB to compile void m140.ecu.c(m140.djz, m140.ebm, m140.edp, m140.ekm, boolean)
[   +1 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +3 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +1 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +2 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
