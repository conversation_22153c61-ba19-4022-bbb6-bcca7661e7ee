[+2754 ms] I/flutter (22450): === FETCH CATEGORIES REQUEST ===
[   +3 ms] I/flutter (22450): URL: https://api.sepesha.com/api/categories
[   +1 ms] I/flutter (22450): Headers: {Content-Type: application/json, Authorization: Bearer
eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************
NS05OGM5LTQ3MDQtYjllOC1lZWZmYjMyMzc2YzIiLCJkYXRhIjp7ImZpcnN0X25hbWUiOiJaWloiLCJtaWRkbGVfbmFtZSI6IlZWViIsImxhc3RfbmFtZSI6IlJSUiIsInBob25lX251bWJlciI6IjcxNDYwOTEzNSIsInBob25lY29kZSI6IjI1NSIsImVtYWlsI
joienp6QHp6ei5jb20iLCJwcml2YWN5X2NoZWNrZWQiOjEsInVpZCI6IjJlNDhlYWE1LTk4YzktNDcwNC1iOWU4LWVlZmZiMzIzNzZjMiJ9fQ.Pn8iUcJU27okXqqE32BQe3RRnnKvZMrxJ2s6GhP1JC4}
[ +218 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +33 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +883 ms] I/flutter (22450): Categories Response Status: 200
[   +2 ms] I/flutter (22450): Categories Response Body: {"status":true,"message":"data found","code":200,"data":[{"id":"1","name":"Bodaboda","photo":null,"icon":null,"description":"Motorcycle taxi
service for quick and affordable
transportation","vehicle_multiplier":"1.0","base_price":"1000","price_per_km":"300","status":"active","created_by":null,"updated_by":null,"deleted_at":null,"created_at":"2025-07-17T22:23:47.000000Z
","updated_at":"2025-07-17T22:23:47.000000Z","capacity":""},{"id":"2","name":"Bajaji","photo":null,"icon":null,"description":"Three-wheeled auto rickshaw for short to medium distance
travel","vehicle_multiplier":"1.2","base_price":"1500","price_per_km":"400","status":"active","created_by":null,"updated_by":null,"deleted_at":null,"created_at":"2025-07-17T22:23:47.000000Z","updat
ed_at":"2025-07-17T22:23:47.000000Z","capacity":""},{"id":"3","name":"Guta","photo":null,"icon":null,"description":"Small car service for comfortable city
travel","vehicle_multiplier":"1.5","base_price":"2000","price_per_km":"500","status":"activ
[   +2 ms] I/flutter (22450): Categories details: []
[   +3 ms] I/flutter (22450): Categories loaded: 5 items
[        ] I/flutter (22450): === FETCH CATEGORIES COMPLETED ===
[ +432 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +22 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +43 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +33 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[+6763 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +25 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +40 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[+1635 ms] I/flutter (22450): === PAYMENT PROVIDER INITIALIZE ===
[   +2 ms] I/flutter (22450): === PAYMENT PROVIDER DEBUG ===
[   +2 ms] I/flutter (22450): API Token: Present (547 chars)
[   +2 ms] I/flutter (22450): Auth Key: 2e48eaa5-98c9-4704-b9e8-eeffb32376c2
[   +1 ms] I/flutter (22450): Phone: 714609135
[   +2 ms] I/flutter (22450): ==============================
[   +1 ms] I/flutter (22450): === INITIALIZING PAYMENT FROM SESSION ===
[   +2 ms] I/flutter (22450): Wallet balance from session: TZS 0.0, USD 0.0
[   +1 ms] I/flutter (22450): === SESSION INITIALIZATION COMPLETE ===
[   +2 ms] I/flutter (22450): === GET AVAILABLE PAYMENT METHODS ===
[   +2 ms] I/flutter (22450): Available Payment Methods: [PaymentMethod(id: cash, type: cash, name: Cash, isDefault: false), PaymentMethod(id: wallet, type: wallet, name: Sepesha Wallet, isDefault:
false), PaymentMethod(id: card, type: card, name: Credit/Debit Card, isDefault: false), PaymentMethod(id: bank, type: bank, name: Bank Transfer, isDefault: false)]
[   +2 ms] I/flutter (22450): ====================================
[   +2 ms] I/flutter (22450): === GET USER PREFERRED PAYMENT METHOD ===
[   +1 ms] I/flutter (22450): Calling PaymentService.getUserPreferredPaymentMethod()...
[   +2 ms] I/flutter (22450): === GET USER PREFERRED PAYMENT METHOD ===
[   +2 ms] I/flutter (22450): Calling _getUserProfile()...
[   +1 ms] I/flutter (22450): === GET USER PROFILE ===
[   +1 ms] I/flutter (22450): Using UserData from Preferences
[   +1 ms] I/flutter (22450): Found user data: ZZZ RRR
[   +1 ms] I/flutter (22450): User Profile Data: {user: Instance of 'UserData', profile_photo_url: null, wallet_balance_tzs: null, wallet_balance_usd: null, preferred_payment_method: null,
is_verified: null, total_rides: null, average_rating: null}
[   +1 ms] I/flutter (22450): ============================
[        ] I/flutter (22450): User profile result: {user: Instance of 'UserData', profile_photo_url: null, wallet_balance_tzs: null, wallet_balance_usd: null, preferred_payment_method: null,       
is_verified: null, total_rides: null, average_rating: null}
[   +1 ms] I/flutter (22450): No preferred payment method found in profile, using default cash
[        ] I/flutter (22450): Default Payment Method: PaymentMethod(id: cash, type: cash, name: Cash, isDefault: true)
[        ] I/flutter (22450): ====================================
[        ] I/flutter (22450): Received preferred method: PaymentMethod(id: cash, type: cash, name: Cash, isDefault: true)
[        ] I/flutter (22450): Set selected payment method: Cash
[        ] I/flutter (22450): Synced with session manager: cash
[        ] I/flutter (22450): ====================================
[        ] I/flutter (22450): Successfully loaded preferred payment method from API
[        ] I/flutter (22450): === GET WALLET BALANCE ===
[        ] I/flutter (22450): === GET USER PROFILE ===
[        ] I/flutter (22450): Using UserData from Preferences
[        ] I/flutter (22450): Found user data: ZZZ RRR
[        ] I/flutter (22450): User Profile Data: {user: Instance of 'UserData', profile_photo_url: null, wallet_balance_tzs: null, wallet_balance_usd: null, preferred_payment_method: null,
is_verified: null, total_rides: null, average_rating: null}
[        ] I/flutter (22450): ============================
[        ] I/flutter (22450): Wallet Balance: WalletBalance(TZS: 0.0, USD: 0.0)
[        ] I/flutter (22450): ========================
[        ] I/flutter (22450): Successfully refreshed wallet balance from API
[        ] I/flutter (22450): === PAYMENT PROVIDER INITIALIZATION COMPLETE ===
[  +77 ms] I/flutter (22450): === FORMAT CURRENCY ===
[   +1 ms] I/flutter (22450): Formatted Currency: TZS 0
[   +1 ms] I/flutter (22450): ======================
[+3640 ms] I/flutter (22450): === STARTING RIDE SEARCH ===
[   +2 ms] I/flutter (22450): Step 1: Calculating fare...
[   +5 ms] I/flutter (22450): === CALCULATE FARE REQUEST ===
[   +2 ms] I/flutter (22450): URL: https://api.sepesha.com/api/calculate-fare
[   +2 ms] I/flutter (22450): Headers: {Content-Type: application/json, Authorization: Bearer
eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************
NS05OGM5LTQ3MDQtYjllOC1lZWZmYjMyMzc2YzIiLCJkYXRhIjp7ImZpcnN0X25hbWUiOiJaWloiLCJtaWRkbGVfbmFtZSI6IlZWViIsImxhc3RfbmFtZSI6IlJSUiIsInBob25lX251bWJlciI6IjcxNDYwOTEzNSIsInBob25lY29kZSI6IjI1NSIsImVtYWlsI
joienp6QHp6ei5jb20iLCJwcml2YWN5X2NoZWNrZWQiOjEsInVpZCI6IjJlNDhlYWE1LTk4YzktNDcwNC1iOWU4LWVlZmZiMzIzNzZjMiJ9fQ.Pn8iUcJU27okXqqE32BQe3RRnnKvZMrxJ2s6GhP1JC4}
[   +2 ms] I/flutter (22450): Request Body: {"pickup_latitude":-6.7251365,"pickup_longitude":39.2149517,"delivery_latitude":-6.7904705,"delivery_longitude":39.25065939999999}
[ +203 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +23 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +33 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +8 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +767 ms] I/flutter (22450): Response Status: 500
[   +1 ms] I/flutter (22450): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date: Fri, 18 Jul 2025
07:05:44 GMT, status: 500 Internal Server Error, content-length: 237, content-type: application/json, via: 0.0 Caddy}
[   +1 ms] I/flutter (22450): Response Body: {"status":false,"message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'settings.settingid' in 'where clause' (Connection: mysql, SQL: select
* from `settings` where `settings`.`settingid` = 1 limit 1)","code":500,"data":null}
[        ] I/flutter (22450): HTTP Error: 500
[        ] I/flutter (22450): Setting mock fare data for testing
[        ] I/flutter (22450): === CALCULATE FARE COMPLETED ===
[        ] I/flutter (22450): Fare calculation completed. Fare data: {distance_km: 5.2, estimated_duration_minutes: 15, fare_estimates: [{vehicle_type: 2 Wheeler, base_fare: 2000.0, distance_fare: 
1500.0, total_fare: 3500.0}, {vehicle_type: 4 Wheeler, base_fare: 3000.0, distance_fare: 2500.0, total_fare: 5500.0}]}
[        ] I/flutter (22450): Step 2: Finding available drivers...
[        ] I/flutter (22450): === FIND AVAILABLE DRIVERS REQUEST ===
[        ] I/flutter (22450): URL: https://api.sepesha.com/api/find-drivers
[        ] I/flutter (22450): Headers: {Content-Type: application/json, Authorization: Bearer
eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************
NS05OGM5LTQ3MDQtYjllOC1lZWZmYjMyMzc2YzIiLCJkYXRhIjp7ImZpcnN0X25hbWUiOiJaWloiLCJtaWRkbGVfbmFtZSI6IlZWViIsImxhc3RfbmFtZSI6IlJSUiIsInBob25lX251bWJlciI6IjcxNDYwOTEzNSIsInBob25lY29kZSI6IjI1NSIsImVtYWlsI
joienp6QHp6ei5jb20iLCJwcml2YWN5X2NoZWNrZWQiOjEsInVpZCI6IjJlNDhlYWE1LTk4YzktNDcwNC1iOWU4LWVlZmZiMzIzNzZjMiJ9fQ.Pn8iUcJU27okXqqE32BQe3RRnnKvZMrxJ2s6GhP1JC4}
[        ] I/flutter (22450): Request Body: {"pickup_latitude":-6.7251365,"pickup_longitude":39.2149517,"vehicle_type":"4 wheeler","radius_km":5,"fee_category_id":"3"}
[ +146 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +33 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +682 ms] I/flutter (22450): Response Status: 442
[   +2 ms] I/flutter (22450): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date: Fri, 18 Jul 2025
07:05:45 GMT, status: 442 unknown status, content-length: 99, content-type: application/json, via: 0.0 Caddy}
[   +2 ms] I/flutter (22450): Response Body: {"status":false,"message":"The fee category id field must be a valid UUID.","code":442,"data":null}
[   +2 ms] I/flutter (22450): HTTP Error: 442
[   +1 ms] I/flutter (22450): === FIND AVAILABLE DRIVERS COMPLETED ===
[   +1 ms] I/flutter (22450): Driver search completed. Found 0 drivers
[   +1 ms] I/flutter (22450): Step 3: Creating booking...
[   +1 ms] I/flutter (22450): === CREATE RIDE BOOKING REQUEST ===
[   +2 ms] I/flutter (22450): URL: https://api.sepesha.com/api/create-ride-booking
[   +2 ms] I/flutter (22450): Headers: {Content-Type: application/json, Authorization: Bearer
eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************
NS05OGM5LTQ3MDQtYjllOC1lZWZmYjMyMzc2YzIiLCJkYXRhIjp7ImZpcnN0X25hbWUiOiJaWloiLCJtaWRkbGVfbmFtZSI6IlZWViIsImxhc3RfbmFtZSI6IlJSUiIsInBob25lX251bWJlciI6IjcxNDYwOTEzNSIsInBob25lY29kZSI6IjI1NSIsImVtYWlsI
joienp6QHp6ei5jb20iLCJwcml2YWN5X2NoZWNrZWQiOjEsInVpZCI6IjJlNDhlYWE1LTk4YzktNDcwNC1iOWU4LWVlZmZiMzIzNzZjMiJ9fQ.Pn8iUcJU27okXqqE32BQe3RRnnKvZMrxJ2s6GhP1JC4}
[   +3 ms] I/flutter (22450): Request Body: {"pickup_latitude":-6.7251365,"pickup_longitude":39.2149517,"delivery_latitude":-6.7904705,"delivery_longitude":39.25065939999999,"pickup_location":"2   
Shamo St, Dar es Salaam, Tanzania","delivery_location":"Victoria Makumbusho","distance_km":5.2,"estimated_fare":5500.0,"payment_method":"cash","customer_id":"2e48eaa5-98c9-4704-b9e8-eeffb32376c2"} 
[ +135 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +628 ms] I/flutter (22450): Response Status: 442
[   +2 ms] I/flutter (22450): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date: Fri, 18 Jul 2025
07:05:46 GMT, status: 442 unknown status, content-length: 90, content-type: application/json, via: 0.0 Caddy}
[   +2 ms] I/flutter (22450): Response Body: {"status":false,"message":"The fee category id field is required.","code":442,"data":null}
[   +1 ms] I/flutter (22450): HTTP Error: 442
[   +2 ms] I/flutter (22450): === CREATE RIDE BOOKING COMPLETED ===
[   +2 ms] I/flutter (22450): Booking creation completed. Booking ID: null
[   +2 ms] I/flutter (22450): No drivers available from API, using mock driver for testing
[   +2 ms] I/flutter (22450): === RIDE SEARCH COMPLETED ===








[  +52 ms] ══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════
           The following assertion was thrown while finalizing the widget tree:
           _HomeScreenState#84507(ticker active) was disposed with an active Ticker.
           _HomeScreenState created a Ticker via its SingleTickerProviderStateMixin, but at the time dispose()
           was called on the mixin, that Ticker was still active. The Ticker must be disposed before calling
           super.dispose().
           Tickers used by AnimationControllers should be disposed by calling dispose() on the
           AnimationController itself. Otherwise, the ticker will leak.
           The offending ticker was:
             Ticker(created by _HomeScreenState#84507)
             The stack trace when the Ticker was actually created was:
             #0      new Ticker.<anonymous closure> (package:flutter/src/scheduler/ticker.dart:86:40)
             #1      new Ticker (package:flutter/src/scheduler/ticker.dart:88:6)
             #2      SingleTickerProviderStateMixin.createTicker
             (package:flutter/src/widgets/ticker_provider.dart:215:15)
             #3      new AnimationController (package:flutter/src/animation/animation_controller.dart:257:21)
             #4      RideProvider.setLoadingController (package:sepesha_app/provider/ride_provider.dart:164:26)
             #5      _HomeScreenState.initState.<anonymous closure>
             (package:sepesha_app/screens/dashboard/home_screen.dart:54:17)
             #6      SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)
             #7      SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1365:11)
             #8      SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1204:5)
             #9      _invoke (dart:ui/hooks.dart:331:13)
             #10     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
             #11     _drawFrame (dart:ui/hooks.dart:303:31)

           When the exception was thrown, this was the stack:
           #0      SingleTickerProviderStateMixin.dispose.<anonymous closure> (package:flutter/src/widgets/ticker_provider.dart:230:7)
           #1      SingleTickerProviderStateMixin.dispose (package:flutter/src/widgets/ticker_provider.dart:244:6)
           #2      _HomeScreenState.dispose (package:sepesha_app/screens/dashboard/home_screen.dart:83:11)
           #3      StatefulElement.unmount (package:flutter/src/widgets/framework.dart:5922:11)
           #4      _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2075:13)
           #5      _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #6      SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #7      _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #8      _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #9      SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #10     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #11     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #12     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #13     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #14     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #15     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #16     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #17     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #18     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #19     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #20     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #21     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #22     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #23     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #24     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #25     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #26     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #27     List.forEach (dart:core-patch/growable_array.dart:425:8)
           #28     SliverMultiBoxAdaptorElement.visitChildren (package:flutter/src/widgets/sliver.dart:1197:52)
           #29     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #30     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #31     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #32     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #33     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #34     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #35     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #36     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #37     MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7118:16)
           #38     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #39     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #40     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #41     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #42     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #43     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #44     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #45     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #46     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #47     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #48     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #49     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #50     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #51     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #52     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #53     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #54     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #55     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #56     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #57     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #58     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #59     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #60     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #61     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #62     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #63     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #64     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #65     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #66     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #67     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #68     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #69     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #70     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #71     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #72     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #73     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #74     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #75     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #76     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #77     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #78     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #79     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #80     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #81     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #82     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #83     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #84     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #85     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #86     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #87     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #88     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #89     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #90     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #91     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #92     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #93     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #94     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #95     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #96     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #97     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #98     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #99     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #100    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #101    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #102    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #103    MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7118:16)
           #104    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #105    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #106    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #107    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #108    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #109    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #110    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #111    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #112    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #113    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #114    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #115    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #116    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #117    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #118    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #119    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #120    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #121    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #122    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #123    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #124    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #125    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #126    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #127    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #128    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #129    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #130    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #131    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #132    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #133    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #134    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #135    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #136    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #137    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #138    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #139    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #140    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #141    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #142    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #143    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #144    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #145    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #146    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #147    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #148    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #149    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #150    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #151    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #152    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #153    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #154    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #155    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #156    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #157    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #158    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #159    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #160    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #161    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #162    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #163    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #164    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #165    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #166    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #167    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #168    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #169    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #170    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #171    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #172    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #173    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #174    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #175    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #176    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #177    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #178    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #179    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #180    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #181    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #182    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #183    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #184    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #185    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #186    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #187    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #188    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #189    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #190    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #191    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #192    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #193    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #194    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #195    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #196    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #197    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #198    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #199    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #200    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #201    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #202    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #203    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #204    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #205    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #206    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #207    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #208    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #209    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #210    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #211    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #212    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #213    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #214    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #215    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #216    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #217    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #218    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #219    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #220    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #221    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #222    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #223    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #224    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #225    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #226    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #227    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #228    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #229    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #230    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #231    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #232    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #233    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #234    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #235    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #236    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #237    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #238    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #239    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #240    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #241    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #242    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #243    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #244    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #245    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #246    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #247    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #248    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #249    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #250    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #251    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #252    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #253    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #254    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #255    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #256    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6994:14)
           #257    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #258    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #259    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #260    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #261    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #262    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #263    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #264    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #265    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #266    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #267    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2073:7)
           #268    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5763:14)
           #269    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2071:13)
           #270    ListIterable.forEach (dart:_internal/iterable.dart:49:13)
           #271    _InactiveElements._unmountAll (package:flutter/src/widgets/framework.dart:2084:25)
           #272    BuildOwner.lockState (package:flutter/src/widgets/framework.dart:2965:15)
           #273    BuildOwner.finalizeTree (package:flutter/src/widgets/framework.dart:3288:7)
           #274    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1247:19)
           #275    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:495:5)
           #276    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)
           #277    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1351:9)
           #278    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1204:5)
           #279    _invoke (dart:ui/hooks.dart:331:13)
           #280    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
           #281    _drawFrame (dart:ui/hooks.dart:303:31)
           ════════════════════════════════════════════════════════════════════════════════════════════════════
[   +8 ms] I/Choreographer(22450): Skipped 46 frames!  The application may be doing too much work on its main thread.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +63 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +23 ms] I/OpenGLRenderer(22450): Davey! duration=867ms; Flags=0, FrameTimelineVsyncId=8448812, IntendedVsync=130723422450196, Vsync=130724189116832, InputEventId=0,
HandleInputStart=130724191615936, AnimationStart=130724191618644, PerformTraversalsStart=130724191970728, DrawStart=130724195347239, FrameDeadline=130723443116862, FrameInterval=130724191506874,   
FrameStartTime=16666666, SyncQueued=130724285332925, SyncStart=130724294806832, IssueDrawCommandsStart=130724294917457, SwapBuffers=130724295528551, FrameCompleted=130724299295009,
DequeueBufferDuration=28854, QueueBufferDuration=1546302, GpuCompleted=130724298366936, SwapBuffersCompleted=130724299295009, DisplayPresentTime=0, 
[  +23 ms] I/GoogleMapController(22450): Installing custom TextureView driven invalidator.
[  +48 ms] I/GoogleMapController(22450): Installing custom TextureView driven invalidator.
[ +214 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] D/Google Android Maps SDK(22450): For capability in capabilities, log:
[   +1 ms] D/Google Android Maps SDK(22450): "AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +22 ms] E/flutter (22450): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: Bad state: Future already completed
[        ] E/flutter (22450): #0      _AsyncCompleter.complete (dart:async/future_impl.dart:100:31)
[        ] E/flutter (22450): #1      _HomeScreenState._buildMap.<anonymous closure> (package:sepesha_app/screens/dashboard/home_screen.dart:115:32)
[        ] E/flutter (22450): #2      _GoogleMapState.onPlatformViewCreated (package:google_maps_flutter/src/google_map.dart:521:19)
[        ] E/flutter (22450): <asynchronous suspension>
[        ] E/flutter (22450): 
[  +12 ms] D/InputConnectionAdaptor(22450): The input method toggled cursor monitoring off
[  +48 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +1 ms] D/ImageReaderSurfaceProducer(22450): ImageTextureEntry can't wait on the fence on Android < 33
[  +37 ms] I/mpany.sepeshap(22450): Background young concurrent copying GC freed 7026KB AllocSpace bytes, 107(6932KB) LOS objects, 37% free, 16MB/27MB, paused 229us,253us total 109.618ms
[  +22 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +44 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +31 ms] I/mpany.sepeshap(22450): Compiler allocated 6714KB to compile void m140.ehv.W(m140.emf, m140.fej, android.content.res.Resources, m140.eka, m140.eut, boolean, m140.djk, java.util.Map,
boolean, boolean, boolean, boolean)
[  +10 ms] W/DynamiteModule(22450): Local module descriptor class for com.google.android.gms.googlecertificates not found.
[   +7 ms] I/DynamiteModule(22450): Considering local module com.google.android.gms.googlecertificates:0 and remote module com.google.android.gms.googlecertificates:7
[   +1 ms] I/DynamiteModule(22450): Selected remote version of com.google.android.gms.googlecertificates, version >= 7
[  +42 ms] W/mpany.sepeshap(22450): ClassLoaderContext classpath element checksum mismatch. expected=*********, found=**********
(DLC[];PCL[base.apk**********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]#PCL[/system/framework/com
.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.e
xtservices/javalib/android.ext.adservices.jar**********]} |
DLC[];PCL[/data/app/~~8j-tqKVgJBrEjShsQvNG2w==/com.sepeshacompany.sepeshapp-F8-bn1UJn1OKlTn4Whg79A==/base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]})
[ +100 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +42 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +105 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +14 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +22 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +22 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +21 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +8 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +18 ms] I/mpany.sepeshap(22450): Background concurrent copying GC freed 2975KB AllocSpace bytes, 124(9844KB) LOS objects, 66% free, 14MB/43MB, paused 416us,857us total 125.932ms
[   +5 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +26 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +3 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +16 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +121 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +70 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +148 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +193 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +137 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +163 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[ +873 ms] E/flutter (22450): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(channel-error, Unable to establish connection on channel:
"dev.flutter.pigeon.google_maps_flutter_android.MapsApi.animateCamera.0"., null, null)
[   +4 ms] E/flutter (22450): #0      MapsApi.animateCamera (package:google_maps_flutter_android/src/messages.g.dart:2278:7)
[   +3 ms] E/flutter (22450): <asynchronous suspension>
[   +1 ms] E/flutter (22450): 
[  +87 ms] E/flutter (22450): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(channel-error, Unable to establish connection on channel:
"dev.flutter.pigeon.google_maps_flutter_android.MapsApi.animateCamera.0"., null, null)
[        ] E/flutter (22450): #0      MapsApi.animateCamera (package:google_maps_flutter_android/src/messages.g.dart:2278:7)
[        ] E/flutter (22450): <asynchronous suspension>
[        ] E/flutter (22450): 
[   +2 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +117 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +3 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +118 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +7 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +1 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +107 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +118 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +117 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +122 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +153 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[  +76 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[ +108 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +74 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +38 ms] I/mpany.sepeshap(22450): Compiler allocated 4483KB to compile void m140.ecu.c(m140.djz, m140.ebm, m140.edp, m140.ekm, boolean)
[  +59 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +12 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] I/mpany.sepeshap(22450): Background concurrent copying GC freed 5389KB AllocSpace bytes, 24(10MB) LOS objects, 60% free, 31MB/79MB, paused 265us,227us total 190.734ms
[   +5 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +7 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] I/mpany.sepeshap(22450): Compiler allocated 5746KB to compile void m140.epo.o()
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] I/mpany.sepeshap(22450): Compiler allocated 4188KB to compile m140.emb m140.emf.l(m140.azx, m140.iai, m140.elt, byte[], boolean, m140.cdn, m140.dex, java.lang.Iterable)
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] W/ProxyAndroidLoggerBackend(22450): Too many Flogger logs received before configuration. Dropping old logs.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] I/mpany.sepeshap(22450): Compiler allocated 4938KB to compile void m140.ecu.c(m140.djz, m140.ebm, m140.edp, m140.ekm, boolean)
[   +1 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +3 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +1 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[        ] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
[   +2 ms] E/FrameEvents(22450): updateAcquireFence: Did not find frame.
